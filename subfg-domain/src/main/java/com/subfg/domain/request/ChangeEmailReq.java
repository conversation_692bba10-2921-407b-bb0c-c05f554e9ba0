package com.subfg.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "换绑邮箱请求")
public class ChangeEmailReq {

    @Schema(description = "新邮箱地址", example = "<EMAIL>")
    @NotBlank(message = "新邮箱地址不能为空")
    @Email(message = "新邮箱格式不正确")
    private String newEmail;

    @Schema(description = "新邮箱验证码", example = "123456")
    @NotBlank(message = "新邮箱验证码不能为空")
    private String newEmailCode;

    @Schema(description = "当前邮箱验证码", example = "654321")
    @NotBlank(message = "当前邮箱验证码不能为空")
    private String currentEmailCode;

}
