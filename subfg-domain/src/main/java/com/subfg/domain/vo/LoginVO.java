package com.subfg.domain.vo;

import com.subfg.domain.entity.user.UserPo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "登录返回值")
public class LoginVo {

    @Schema(description = "用户信息")
    private UserPo user;

    @Schema(description = "token")
    private String token;

    @Schema(description = "token过期时间")
    private Long expireTime;
}
