# Common messages
common.success=Operation successful
common.error=Operation failed
common.save.success=Save successful
common.save.error=Save failed
common.delete.success=Delete successful
common.delete.error=Delete failed
common.update.success=Update successful
common.update.error=Update failed
common.query.success=Query successful
common.query.error=Query failed
common.not.found=Data not found
common.already.exists=Data already exists
common.invalid.parameter=Invalid parameter
common.access.denied=Access denied
common.system.error=System error

# User related
user.login.success=Login successful
user.login.error=Login failed
user.logout.success=Logout successful
user.register.success=Registration successful
user.register.error=Registration failed
user.password.error=Password error
user.not.found=User not found
user.already.exists=User already exists
user.disabled=User is disabled
user.email.invalid=Invalid email format
user.password.weak=Password is too weak
user.updateUserInfo.success=Personal information updated successfully
user.update.failed=Failed to update user information
user.email.already.exists=Email is already used by another user
user.phone.already.exists=Phone number is already used by another user
user.username.already.exists=Username is already used by another user
user.bindThirdPartyAccount.success=Third-party account bound successfully
user.bind.platform.not.supported=Unsupported third-party platform type
user.bind.wechat.already.bound=This WeChat account is already bound to another user
user.bind.wechat.already.bound.current=You have already bound a WeChat account, please unbind first
user.bind.failed=Binding failed, please try again later
user.changeEmail.success=Email changed successfully
user.current.email.code.error=Current email verification code is incorrect or expired
user.new.email.code.error=New email verification code is incorrect or expired
user.change.email.failed=Failed to change email, please try again later

# Family group related
family.group.create.success=Family group created successfully
family.group.create.error=Failed to create family group
family.group.join.success=Joined family group successfully
family.group.join.error=Failed to join family group
family.group.leave.success=Left family group successfully
family.group.leave.error=Failed to leave family group
family.group.not.found=Family group not found
family.group.full=Family group is full
family.group.already.joined=Already joined this family group
family.group.not.joined=Not joined this family group
family.group.permission.denied=No permission to operate this family group

# Product related
product.not.found=Product not found
product.unavailable=Product unavailable
product.price.changed=Product price has changed

# Order related
order.create.success=Order created successfully
order.create.error=Failed to create order
order.pay.success=Payment successful
order.pay.error=Payment failed
order.cancel.success=Order cancelled successfully
order.cancel.error=Failed to cancel order
order.not.found=Order not found
order.status.invalid=Invalid order status

# Status related
status.active=Active
status.inactive=Inactive
status.pending=Pending
status.approved=Approved
status.rejected=Rejected
status.expired=Expired
status.cancelled=Cancelled

# Time related
time.today=Today
time.yesterday=Yesterday
time.tomorrow=Tomorrow
time.this.week=This week
time.this.month=This month
time.this.year=This year

# Validation messages
validation.required={0} cannot be empty
validation.email.invalid=Invalid email format
validation.phone.invalid=Invalid phone number format
validation.length.min={0} length cannot be less than {1} characters
validation.length.max={0} length cannot exceed {1} characters
validation.length.range={0} length must be between {1} and {2} characters
validation.number.min={0} cannot be less than {1}
validation.number.max={0} cannot be greater than {1}
validation.number.range={0} must be between {1} and {2}
validation.pattern.invalid={0} format is incorrect
validation.date.invalid=Invalid date format
validation.future.date={0} must be a future date
validation.past.date={0} must be a past date

# Field names
field.username=Username
field.password=Password
field.email=Email
field.phone=Phone
field.name=Name
field.age=Age
field.birthday=Birthday
field.address=Address
field.description=Description
field.title=Title
field.content=Content
field.amount=Amount
field.quantity=Quantity
field.price=Price

# Error messages
error.400=Bad request
error.401=Unauthorized
error.403=Forbidden
error.404=Resource not found
error.405=Method not allowed
error.415=Unsupported media type: {0}
error.500=Internal server error
error.502=Bad gateway
error.503=Service unavailable
error.504=Gateway timeout

# Business errors
error.business.user.not.login=User not logged in
error.business.user.permission.denied=Insufficient user permissions
error.business.data.not.found=Data not found
error.business.data.duplicate=Duplicate data
error.business.operation.failed=Operation failed
error.business.parameter.invalid=Invalid parameter
error.business.file.upload.failed=File upload failed
error.business.file.download.failed=File download failed
error.business.network.timeout=Network timeout
error.business.database.error=Database error
