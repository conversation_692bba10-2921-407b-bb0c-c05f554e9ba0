package com.subfg.subfgapi.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.subfg.domain.request.UpdateUserInfoReq;
import com.subfg.domain.vo.Result;
import com.subfg.subfgapi.Serivce.UserService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/user")
@Tag(name = "用户管理", description = "用户管理相关接口")
public class UserController {

    private final UserService userService;

    /**
     * 用户修改个人信息
     */
    @PostMapping("/updateUserInfo")
    @Operation(summary = "用户修改个人信息", description = "用户修改个人信息")
    public Result<String> updateUserInfo(@RequestBody UpdateUserInfoReq req){
        userService.updateUserInfo(req);
        return Result.successI18n("user.updateUserInfo.success");
    }
    
}
