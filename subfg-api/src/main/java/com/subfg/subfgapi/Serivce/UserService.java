package com.subfg.subfgapi.Serivce;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.subfg.common.constans.RedisConstants;
import com.subfg.common.exception.BusinessException;
import com.subfg.common.util.RedisUtil;
import com.subfg.common.util.TimeUtil;
import com.subfg.domain.dto.wx.WxAccessTokenDto;
import com.subfg.domain.dto.wx.WxUserDetailDto;
import com.subfg.domain.entity.user.UserAuthPo;
import com.subfg.domain.entity.user.UserPo;
import com.subfg.domain.enums.ThirdPlatformType;
import com.subfg.domain.request.BindThirdPartyAccountReq;
import com.subfg.domain.request.ChangeEmailReq;
import com.subfg.domain.request.UpdateUserInfoReq;
import com.subfg.repository.mapper.UserAuthMapper;
import com.subfg.repository.mapper.UserMapper;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserMapper userMapper;
    private final UserAuthMapper userAuthMapper;
    private final WxService wxService;
    private final RedisUtil redisUtil;

    /**
     * 修改用户个人信息
     */
    public void updateUserInfo(UpdateUserInfoReq req){
        // 1. 获取当前登录用户ID
        String currentUserId = StpUtil.getLoginIdAsString();

        // 2. 查询当前用户信息
        UserPo currentUser = userMapper.selectById(currentUserId);
        if (currentUser == null) {
            throw new BusinessException("user.not.exists");
        }

        // 3. 验证用户是否被禁用
        if (!currentUser.getEnable()) {
            throw new BusinessException("user.disabled");
        }

        // 4. 验证邮箱是否被其他用户使用
        if (StringUtils.hasText(req.getEmail()) && !req.getEmail().equals(currentUser.getEmail())) {
            UserPo existingUser = userMapper.selectOne(
                new LambdaQueryWrapper<UserPo>()
                    .eq(UserPo::getEmail, req.getEmail())
                    .ne(UserPo::getUserId, currentUserId)
            );
            if (existingUser != null) {
                throw new BusinessException("user.email.already.exists");
            }
        }

        // 5. 验证手机号是否被其他用户使用
        if (StringUtils.hasText(req.getPhone())) {
            Long phoneNumber = Long.valueOf(req.getPhone());
            if (!phoneNumber.equals(currentUser.getPhone())) {
                UserPo existingUser = userMapper.selectOne(
                    new LambdaQueryWrapper<UserPo>()
                        .eq(UserPo::getPhone, phoneNumber)
                        .ne(UserPo::getUserId, currentUserId)
                );
                if (existingUser != null) {
                    throw new BusinessException("user.phone.already.exists");
                }
            }
        }

        // 6. 验证用户名是否被其他用户使用
        if (StringUtils.hasText(req.getUserName()) && !req.getUserName().equals(currentUser.getUserName())) {
            UserPo existingUser = userMapper.selectOne(
                new LambdaQueryWrapper<UserPo>()
                    .eq(UserPo::getUserName, req.getUserName())
                    .ne(UserPo::getUserId, currentUserId)
            );
            if (existingUser != null) {
                throw new BusinessException("user.username.already.exists");
            }
        }

        // 7. 更新用户信息（只更新非空字段）
        UserPo updateUser = new UserPo().setUserId(currentUserId);
        boolean hasUpdate = false;

        if (StringUtils.hasText(req.getUserName())) {
            updateUser.setUserName(req.getUserName());
            hasUpdate = true;
        }

        if (StringUtils.hasText(req.getEmail())) {
            updateUser.setEmail(req.getEmail());
            hasUpdate = true;
        }

        if (StringUtils.hasText(req.getPhone())) {
            updateUser.setPhone(Long.valueOf(req.getPhone()));
            hasUpdate = true;
        }

        if (StringUtils.hasText(req.getAvatarUrl())) {
            updateUser.setAvatarUrl(req.getAvatarUrl());
            hasUpdate = true;
        }

        if (StringUtils.hasText(req.getMotto())) {
            updateUser.setMotto(req.getMotto());
            hasUpdate = true;
        }

        // 8. 执行更新
        if (hasUpdate) {
            int result = userMapper.updateById(updateUser);
            if (result <= 0) {
                throw new BusinessException("user.update.failed");
            }
            log.info("用户信息更新成功 - 用户ID: {}", currentUserId);
        } else {
            log.info("用户信息无变更 - 用户ID: {}", currentUserId);
        }
    }

    /**
     * 绑定三方账号
     */
    public void bindThirdPartyAccount(BindThirdPartyAccountReq req) {
        // 1. 获取当前登录用户ID
        String currentUserId = StpUtil.getLoginIdAsString();

        // 2. 查询当前用户信息
        UserPo currentUser = userMapper.selectById(currentUserId);
        if (currentUser == null) {
            throw new BusinessException("user.not.exists");
        }

        // 3. 验证用户是否被禁用
        if (!currentUser.getEnable()) {
            throw new BusinessException("user.disabled");
        }

        // 4. 验证第三方平台类型
        ThirdPlatformType platformType = ThirdPlatformType.fromCode(req.getThirdPartyType());
        if (platformType == null) {
            throw new BusinessException("user.bind.platform.not.supported");
        }

        // 5. 目前只支持微信绑定
        if (platformType != ThirdPlatformType.WECHAT) {
            throw new BusinessException("user.bind.platform.not.supported");
        }

        // 6. 通过code获取微信用户信息
        WxAccessTokenDto wxAccessTokenDto = wxService.getWxAccessToken(req.getCode());
        WxUserDetailDto wxUserDetailDto = wxService.getWxUserInfo(wxAccessTokenDto.getAccess_token(), wxAccessTokenDto.getOpenid());

        // 7. 检查该微信账号是否已被其他用户绑定
        UserPo existingUserByWechat = userMapper.selectOne(
            new LambdaQueryWrapper<UserPo>()
                .eq(UserPo::getWechat, wxUserDetailDto.getUnionid())
                .ne(UserPo::getUserId, currentUserId)
        );
        if (existingUserByWechat != null) {
            throw new BusinessException("user.bind.wechat.already.bound");
        }

        // 8. 检查该微信账号是否已在userauth表中被其他用户绑定
        UserAuthPo existingAuth = userAuthMapper.selectOne(
            new LambdaQueryWrapper<UserAuthPo>()
                .eq(UserAuthPo::getUuid, wxUserDetailDto.getUnionid())
                .eq(UserAuthPo::getSource, ThirdPlatformType.WECHAT.getCode())
                .ne(UserAuthPo::getUserId, currentUserId)
                .eq(UserAuthPo::getEnable, true)
        );
        if (existingAuth != null) {
            throw new BusinessException("user.bind.wechat.already.bound");
        }

        // 9. 检查当前用户是否已绑定微信账号
        UserAuthPo currentUserAuth = userAuthMapper.selectOne(
            new LambdaQueryWrapper<UserAuthPo>()
                .eq(UserAuthPo::getUserId, currentUserId)
                .eq(UserAuthPo::getSource, ThirdPlatformType.WECHAT.getCode())
                .eq(UserAuthPo::getEnable, true)
        );
        if (currentUserAuth != null) {
            throw new BusinessException("user.bind.wechat.already.bound.current");
        }

        // 10. 插入userauth表数据
        UserAuthPo userAuthPo = new UserAuthPo()
            .setUserId(currentUserId)
            .setUuid(wxUserDetailDto.getUnionid())
            .setThirdAccount(wxUserDetailDto.getOpenid())
            .setSource(ThirdPlatformType.WECHAT.getCode())
            .setAvatarUrl(wxUserDetailDto.getHeadimgurl())
            .setName(wxUserDetailDto.getNickName())
            .setEnable(true)
            .setCreateTime(TimeUtil.getCurrentTimestamp());

        int authResult = userAuthMapper.insert(userAuthPo);
        if (authResult <= 0) {
            throw new BusinessException("user.bind.failed");
        }

        // 11. 更新user表的wechat字段
        UserPo updateUser = new UserPo()
            .setUserId(currentUserId)
            .setWechat(wxUserDetailDto.getUnionid());

        int userResult = userMapper.updateById(updateUser);
        if (userResult <= 0) {
            throw new BusinessException("user.bind.failed");
        }

        log.info("用户绑定微信账号成功 - 用户ID: {}, 微信unionid: {}", currentUserId, wxUserDetailDto.getUnionid());
    }

    /**
     * 换绑邮箱
     */
    public void changeEmail(ChangeEmailReq req) {
        // 1. 获取当前登录用户ID
        String currentUserId = StpUtil.getLoginIdAsString();

        // 2. 查询当前用户信息
        UserPo currentUser = userMapper.selectById(currentUserId);
        if (currentUser == null) {
            throw new BusinessException("user.not.exists");
        }

        // 3. 验证用户是否被禁用
        if (!currentUser.getEnable()) {
            throw new BusinessException("user.disabled");
        }

        // 4. 验证新邮箱是否已被其他用户使用
        UserPo existingUser = userMapper.selectOne(
            new LambdaQueryWrapper<UserPo>()
                .eq(UserPo::getEmail, req.getNewEmail())
        );
        if (existingUser != null) {
            throw new BusinessException("user.email.already.exists");
        }

        // 5. 验证当前邮箱验证码
        String currentEmailRedisKey = RedisConstants.EMAIL_CODE_KEY + currentUser.getEmail();
        String currentEmailCode = redisUtil.getString(currentEmailRedisKey);
        if (currentEmailCode == null || !currentEmailCode.equals(req.getCurrentEmailCode())) {
            throw new BusinessException("user.current.email.code.error");
        }

        // 6. 验证新邮箱验证码
        String newEmailRedisKey = RedisConstants.EMAIL_CODE_KEY + req.getNewEmail();
        String newEmailCode = redisUtil.getString(newEmailRedisKey);
        if (newEmailCode == null || !newEmailCode.equals(req.getNewEmailCode())) {
            throw new BusinessException("user.new.email.code.error");
        }

        // 7. 更新用户邮箱
        UserPo updateUser = new UserPo()
            .setUserId(currentUserId)
            .setEmail(req.getNewEmail());

        int result = userMapper.updateById(updateUser);
        if (result <= 0) {
            throw new BusinessException("user.change.email.failed");
        }

        // 8. 删除验证码缓存
        redisUtil.delete(currentEmailRedisKey);
        redisUtil.delete(newEmailRedisKey);

        log.info("用户换绑邮箱成功 - 用户ID: {}, 原邮箱: {}, 新邮箱: {}",
                currentUserId, currentUser.getEmail(), req.getNewEmail());
    }

}
