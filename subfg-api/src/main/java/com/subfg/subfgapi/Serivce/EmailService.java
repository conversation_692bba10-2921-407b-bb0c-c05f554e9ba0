package com.subfg.subfgapi.Serivce;

import java.io.UnsupportedEncodingException;
import java.util.Properties;

import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.subfg.domain.entity.config.ConfigEmailPo;

import jakarta.annotation.PostConstruct;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 邮件发送服务
 * 提供同步和异步邮件发送功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmailService {

    private final EmailConfigService emailConfigService;
    private JavaMailSender mailSender;

    private final String personal = "SUBFG";
    private String systemSendAccount = "<EMAIL>";

    @PostConstruct
    public void init() {

        ConfigEmailPo emailConfig = this.getEmailConfig();
        if (emailConfig == null) {
            throw new RuntimeException("邮件配置不存在");
        }
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost(emailConfig.getHost());
        mailSender.setPort(emailConfig.getPort());
        mailSender.setUsername(emailConfig.getAccount());
        mailSender.setPassword(emailConfig.getPassword());
        mailSender.setProtocol(emailConfig.getProtocol());
        mailSender.setDefaultEncoding("UTF-8");
        Properties properties = mailSender.getJavaMailProperties();
        properties.put("mail.smtp.auth", "true");
        properties.put("mail.smtp.timeout", "10000");
        properties.put("mail.smtp.ssl.enable", "true");
        properties.put("mail.smtp.starttls.enable", "true");
        this.mailSender = mailSender;
        log.info("邮件配置初始化完成{}",emailConfig);
    }

    /**
     * 从数据库获取邮箱配置并解密敏感信息
     *
     * @return 解密后的邮箱配置
     */
    private ConfigEmailPo getEmailConfig() {
        try {
            // 使用 EmailConfigService 获取解密后的邮箱配置
            ConfigEmailPo config = emailConfigService.getEmailConfig();
            if (config == null) {
                log.error("数据库中未找到邮箱配置");
                return null;
            }
            return config;

        } catch (Exception e) {
            log.error("获取邮箱配置失败", e);
            return null;
        }
    }

    /**
     * 发送简单文本邮件（同步）
     *
     * @param to      收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     */
    public void sendSimpleEmail(String to, String subject, String content) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);
            
            mailSender.send(message);
            log.info("邮件发送成功 - 收件人: {}, 主题: {}", to, subject);
        } catch (Exception e) {
            log.error("邮件发送失败 - 收件人: {}, 主题: {}", to, subject, e);
            throw new RuntimeException("邮件发送失败", e);
        }
    }

    /**
     * 发送HTML邮件（同步）
     *
     * @param to      收件人邮箱
     * @param subject 邮件主题
     * @param content HTML内容
     */
    public void sendHtmlEmail(String to, String subject, String content) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            InternetAddress from = new InternetAddress(systemSendAccount, personal);
            message.setFrom(from);
            
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true); // true表示HTML格式
            
            mailSender.send(message);
            log.info("HTML邮件发送成功 - 收件人: {}, 主题: {}", to, subject);
        } catch (MessagingException | UnsupportedEncodingException e) {
            log.error("HTML邮件发送失败 - 收件人: {}, 主题: {}", to, subject, e);
            throw new RuntimeException("HTML邮件发送失败", e);
        }
    }

    /**
     * 异步发送简单文本邮件
     *
     * @param to      收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     */
    @Async("fastTaskExecutor")
    public void sendSimpleEmailAsync(String to, String subject, String content) {
        sendSimpleEmail(to, subject, content);
    }

    /**
     * 发送验证码邮件（异步）
     *
     * @param to   收件人邮箱
     * @param code 验证码
     * @param type 验证码类型：1-注册,2-登录,3-忘记密码
     */
    @Async("fastTaskExecutor")
    public void sendVerifyCodeEmail(String to, String code, Integer type) {
        log.info("开始异步发送验证码邮件 - 线程: {}, 邮箱: {}", Thread.currentThread().getName(), to);

        String subject = getVerifyCodeSubject(type);
        String content = buildVerifyCodeContent(code, type);

        sendHtmlEmail(to, subject, content);

        log.info("验证码邮件发送完成 - 线程: {}, 邮箱: {}", Thread.currentThread().getName(), to);
    }

    /**
     * 根据类型获取验证码邮件主题
     */
    private String getVerifyCodeSubject(Integer type) {
        return switch (type) {
            case 1 -> "【SUBFG】注册验证码";
            case 2 -> "【SUBFG】登录验证码";
            case 3 -> "【SUBFG】密码重置验证码";
            case 4 -> "【SUBFG】换绑邮箱验证码";
            default -> "【SUBFG】验证码";
        };
    }

    /**
     * 构建验证码邮件内容
     */
    private String buildVerifyCodeContent(String code, Integer type) {
        String purpose = switch (type) {
            case 1 -> "注册账号";
            case 2 -> "登录账号";
            case 3 -> "重置密码";
            case 4 -> "换绑邮箱";
            default -> "验证身份";
        };

        return String.format("""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #2c3e50;">SUBFG</h1>
                    </div>
                    
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                        <h2 style="color: #2c3e50; margin-top: 0;">验证码通知</h2>
                        <p>您好！</p>
                        <p>您正在进行<strong>%s</strong>操作，验证码为：</p>
                        
                        <div style="text-align: center; margin: 30px 0;">
                            <span style="display: inline-block; background-color: #3498db; color: white; 
                                         padding: 15px 30px; font-size: 24px; font-weight: bold; 
                                         border-radius: 6px; letter-spacing: 3px;">%s</span>
                        </div>
                        
                        <p style="color: #e74c3c; font-weight: bold;">验证码5分钟内有效，请及时使用。</p>
                        <p style="color: #7f8c8d; font-size: 14px;">
                            如果这不是您本人的操作，请忽略此邮件。
                        </p>
                    </div>
                    
                    <div style="text-align: center; color: #7f8c8d; font-size: 12px;">
                        <p>此邮件由系统自动发送，请勿回复。</p>
                        <p>&copy; 2024 SUBFG. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """, purpose, code);
    }
}
