# 生产环境配置
spring:
  profiles:
    active: prod

# 跨域配置 - 生产环境
cors:
  # 生产环境指定具体的前端域名，多个用逗号分隔
  # 示例：https://www.subfg.com,https://app.subfg.com
  allowed-origins: "https://www.subfg.com,https://app.subfg.com"
  # 预检请求缓存时间（秒）
  max-age: 7200

# 日志配置 - 生产环境
logging:
  level:
    com.subfg: INFO
    org.springframework.web: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
